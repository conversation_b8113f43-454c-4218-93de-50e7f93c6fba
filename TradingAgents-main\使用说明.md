# TradingAgents 多智能体量化交易系统 - 使用说明

## 🎯 系统简介

TradingAgents是一个基于大语言模型(LLM)的多智能体量化交易框架，模拟真实交易公司的运作模式。系统通过部署专业的LLM驱动的智能体来协作评估市场条件并做出交易决策。

## 🏗️ 系统架构

### 📊 分析师团队
- **基本面分析师**: 评估公司财务和业绩指标，识别内在价值和潜在风险
- **情绪分析师**: 分析社交媒体和公众情绪，使用情绪评分算法评估短期市场情绪
- **新闻分析师**: 监控全球新闻和宏观经济指标，解释事件对市场条件的影响
- **技术分析师**: 使用技术指标(如MACD和RSI)检测交易模式并预测价格走势

### 🔬 研究团队
- **看涨研究员**: 批判性评估分析师团队提供的见解，寻找潜在收益
- **看跌研究员**: 通过结构化辩论平衡潜在收益与固有风险

### 💼 交易员
- 综合分析师和研究员的报告做出明智的交易决策
- 基于全面的市场洞察确定交易时机和规模

### ⚖️ 风险管理团队
- 通过评估市场波动性、流动性和其他风险因素持续评估投资组合风险
- 评估和调整交易策略，向投资组合经理提供评估报告

### 📈 投资组合经理
- 批准/拒绝交易提案
- 如果批准，订单将发送到模拟交易所并执行

## 🚀 部署和运行

### 1. 环境准备

系统已经部署在: `d:\GP\TradingAgents\TradingAgents-main`

依赖已安装完成，包括:
- Python 3.12.3
- 所有必需的Python包

### 2. API密钥配置

#### 获取API密钥:
1. **OpenAI API密钥**: 访问 https://platform.openai.com/api-keys
2. **FinnHub API密钥**: 访问 https://finnhub.io/register (免费版本即可)

#### 设置环境变量:
```powershell
# 在PowerShell中设置
$env:OPENAI_API_KEY="your-openai-api-key"
$env:FINNHUB_API_KEY="your-finnhub-api-key"
```

### 3. 运行方式

#### 方式一: 命令行界面 (CLI)
```powershell
python -m cli.main
```
- 交互式界面，可以选择股票代码、分析日期、分析师团队等
- 实时显示分析进度

#### 方式二: 直接运行主程序
```powershell
python main.py
```
- 直接分析NVDA股票
- 使用预设配置

#### 方式三: 自定义Python脚本
```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# 创建自定义配置
config = DEFAULT_CONFIG.copy()
config["deep_think_llm"] = "gpt-4o-mini"
config["quick_think_llm"] = "gpt-4o-mini"
config["max_debate_rounds"] = 2
config["online_tools"] = True

# 初始化系统
ta = TradingAgentsGraph(debug=True, config=config)

# 运行分析
_, decision = ta.propagate("AAPL", "2024-12-01")
print(decision)
```

### 4. 演示脚本
```powershell
python demo.py
```
- 展示系统架构和功能
- 检查环境配置
- 不需要真实API密钥即可运行

## ⚙️ 配置选项

### LLM配置
- `llm_provider`: LLM提供商 ("openai", "google", "anthropic")
- `deep_think_llm`: 深度思考模型 (如 "gpt-4o", "o1-preview")
- `quick_think_llm`: 快速思考模型 (如 "gpt-4o-mini")

### 辩论配置
- `max_debate_rounds`: 最大辩论轮数
- `max_risk_discuss_rounds`: 最大风险讨论轮数

### 工具配置
- `online_tools`: 是否使用在线工具获取实时数据

## 📊 输出结果

系统会输出详细的分析报告，包括:
1. **分析师报告**: 各个分析师的专业分析
2. **研究团队辩论**: 看涨和看跌观点的辩论
3. **交易建议**: 具体的买入/卖出/持有建议
4. **风险评估**: 风险管理团队的评估
5. **最终决策**: 投资组合经理的最终决定

## ⚠️ 重要提醒

1. **仅供研究使用**: 本框架仅用于研究目的，不构成投资建议
2. **API成本**: 系统会进行大量API调用，请注意API使用成本
3. **数据准确性**: 交易表现可能因多种因素而异，包括模型选择、数据质量等
4. **风险提示**: 投资有风险，请谨慎决策

## 🔧 故障排除

### 常见问题:
1. **API密钥错误**: 确保API密钥正确设置且有效
2. **网络连接**: 确保网络连接正常
3. **依赖问题**: 如有包缺失，运行 `pip install -r requirements.txt`

### 获取帮助:
- GitHub仓库: https://github.com/TauricResearch/TradingAgents
- 论文: https://arxiv.org/abs/2412.20138
- 社区: https://discord.com/invite/hk9PGKShPK

## 📈 下一步

1. 设置真实的API密钥
2. 尝试分析不同的股票
3. 调整配置参数优化性能
4. 探索不同的LLM模型
5. 分析历史数据进行回测

祝您使用愉快！🎉
