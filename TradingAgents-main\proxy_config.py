#!/usr/bin/env python3
"""
代理配置工具
帮助配置OpenAI API的代理连接
"""

import os
from openai import OpenAI
import requests

def configure_proxy_for_openai():
    """配置OpenAI的代理设置"""
    print("🔧 配置OpenAI代理连接")
    print("=" * 50)
    
    print("请选择您的代理类型:")
    print("1. Clash (默认端口 7890)")
    print("2. V2Ray (默认端口 1080)")
    print("3. 其他代理")
    print("4. 手动输入代理地址")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    proxy_url = None
    
    if choice == "1":
        proxy_url = "http://127.0.0.1:7890"
    elif choice == "2":
        proxy_url = "http://127.0.0.1:1080"
    elif choice == "3":
        proxy_url = "http://127.0.0.1:8080"
    elif choice == "4":
        proxy_url = input("请输入代理地址 (格式: http://ip:port): ").strip()
    else:
        print("❌ 无效选择")
        return None
    
    return proxy_url

def test_proxy_connection(proxy_url):
    """测试代理连接"""
    print(f"\n🔍 测试代理连接: {proxy_url}")
    
    try:
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        # 测试基本连接
        response = requests.get(
            "https://api.openai.com",
            proxies=proxies,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 代理连接成功!")
            return True
        else:
            print(f"❌ 代理连接失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return False

def test_openai_with_proxy(proxy_url):
    """使用代理测试OpenAI API"""
    print(f"\n🤖 使用代理测试OpenAI API...")
    
    try:
        # 设置环境变量
        os.environ["HTTP_PROXY"] = proxy_url
        os.environ["HTTPS_PROXY"] = proxy_url
        os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
        
        # 创建客户端
        client = OpenAI(
            timeout=30.0,
            http_client=None  # 使用默认的httpx客户端，会自动使用环境变量中的代理
        )
        
        # 测试请求
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": "请回复'代理连接成功'"}
            ],
            max_tokens=10
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI API通过代理工作正常: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API代理测试失败: {e}")
        return False

def create_proxy_config_file(proxy_url):
    """创建代理配置文件"""
    config_content = f'''#!/usr/bin/env python3
"""
代理配置文件
自动设置OpenAI API的代理
"""

import os

def setup_proxy():
    """设置代理环境变量"""
    proxy_url = "{proxy_url}"
    
    os.environ["HTTP_PROXY"] = proxy_url
    os.environ["HTTPS_PROXY"] = proxy_url
    
    print(f"✅ 代理已设置: {{proxy_url}}")

if __name__ == "__main__":
    setup_proxy()
'''
    
    with open("proxy_setup.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print(f"✅ 代理配置文件已创建: proxy_setup.py")

def main():
    print("🌐 OpenAI API代理配置工具")
    print("=" * 50)
    
    print("⚠️  注意事项:")
    print("1. 确保您的代理软件正在运行")
    print("2. 确认代理端口号正确")
    print("3. 代理需要支持HTTPS连接")
    
    # 配置代理
    proxy_url = configure_proxy_for_openai()
    
    if not proxy_url:
        return
    
    # 测试代理连接
    if not test_proxy_connection(proxy_url):
        print("\n❌ 代理连接失败，请检查:")
        print("   1. 代理软件是否正在运行")
        print("   2. 端口号是否正确")
        print("   3. 防火墙设置")
        return
    
    # 测试OpenAI API
    if test_openai_with_proxy(proxy_url):
        print("\n🎉 代理配置成功!")
        
        # 创建配置文件
        create_proxy_config_file(proxy_url)
        
        print("\n📝 使用方法:")
        print("1. 在运行TradingAgents前先运行:")
        print("   python proxy_setup.py")
        print("2. 然后运行A股分析:")
        print("   python main.py")
        
        print("\n💡 或者在代码中添加:")
        print(f"   os.environ['HTTP_PROXY'] = '{proxy_url}'")
        print(f"   os.environ['HTTPS_PROXY'] = '{proxy_url}'")
        
    else:
        print("\n❌ OpenAI API测试失败")
        print("请检查:")
        print("1. API密钥是否有效")
        print("2. 代理是否支持HTTPS")
        print("3. 网络连接是否稳定")

if __name__ == "__main__":
    main()
