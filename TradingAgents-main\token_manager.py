#!/usr/bin/env python3
"""
Token管理工具 - 用于管理SiliconFlow API的token限制
"""

import tiktoken
from typing import List, Dict, Any

class TokenManager:
    """管理API调用的token使用"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo", max_tokens: int = 32768):
        """
        初始化Token管理器
        
        Args:
            model_name: 模型名称
            max_tokens: 最大token限制
        """
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.safety_margin = 0.8  # 安全边际，使用80%的token限制
        self.effective_limit = int(max_tokens * self.safety_margin)
        
        try:
            self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except:
            # 如果模型不支持，使用默认编码
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        try:
            return len(self.encoding.encode(text))
        except:
            # 简单估算：1个token约等于4个字符
            return len(text) // 4
    
    def truncate_text(self, text: str, max_tokens: int = None) -> str:
        """截断文本以符合token限制"""
        if max_tokens is None:
            max_tokens = self.effective_limit
        
        current_tokens = self.count_tokens(text)
        if current_tokens <= max_tokens:
            return text
        
        # 按比例截断
        ratio = max_tokens / current_tokens
        target_length = int(len(text) * ratio * 0.9)  # 再留10%安全边际
        
        # 尝试在句号或换行处截断
        truncated = text[:target_length]
        
        # 寻找最后的句号或换行
        last_period = truncated.rfind('。')
        last_newline = truncated.rfind('\n')
        last_break = max(last_period, last_newline)
        
        if last_break > target_length * 0.7:  # 如果截断点不会丢失太多内容
            truncated = truncated[:last_break + 1]
        
        return truncated + "\n\n[数据已截断以符合token限制]"
    
    def optimize_data_for_analysis(self, data: str, data_type: str = "stock_data") -> str:
        """针对不同数据类型优化token使用"""
        
        if data_type == "stock_data":
            return self._optimize_stock_data(data)
        elif data_type == "news_data":
            return self._optimize_news_data(data)
        elif data_type == "financial_data":
            return self._optimize_financial_data(data)
        else:
            return self.truncate_text(data)
    
    def _optimize_stock_data(self, data: str) -> str:
        """优化股票数据"""
        lines = data.split('\n')
        
        # 保留标题和表头
        header_lines = []
        data_lines = []
        
        for line in lines:
            if line.startswith('#') or line.startswith('日期') or line.startswith('-'):
                header_lines.append(line)
            elif line.strip() and not line.startswith('('):
                data_lines.append(line)
        
        # 只保留最近的数据
        max_data_lines = 20
        if len(data_lines) > max_data_lines:
            data_lines = data_lines[-max_data_lines:]
            header_lines.append(f"(显示最近{max_data_lines}条数据)")
        
        optimized = '\n'.join(header_lines + data_lines)
        return self.truncate_text(optimized, max_tokens=8000)  # 为股票数据分配8000 tokens
    
    def _optimize_news_data(self, data: str) -> str:
        """优化新闻数据"""
        # 新闻数据通常很长，需要大幅压缩
        lines = data.split('\n')
        
        # 保留标题和前几条新闻
        important_lines = []
        news_count = 0
        max_news = 10
        
        for line in lines:
            if line.startswith('#') and news_count < max_news:
                important_lines.append(line)
                news_count += 1
            elif line.strip() and len(line) < 200:  # 只保留较短的描述
                important_lines.append(line)
        
        optimized = '\n'.join(important_lines)
        return self.truncate_text(optimized, max_tokens=6000)  # 为新闻数据分配6000 tokens
    
    def _optimize_financial_data(self, data: str) -> str:
        """优化财务数据"""
        # 财务数据通常比较简洁，主要是确保格式清晰
        return self.truncate_text(data, max_tokens=4000)  # 为财务数据分配4000 tokens
    
    def check_total_tokens(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """检查消息列表的总token数"""
        total_tokens = 0
        
        for message in messages:
            if isinstance(message, dict) and 'content' in message:
                total_tokens += self.count_tokens(str(message['content']))
        
        return {
            'total_tokens': total_tokens,
            'max_tokens': self.max_tokens,
            'effective_limit': self.effective_limit,
            'within_limit': total_tokens <= self.effective_limit,
            'usage_percentage': (total_tokens / self.max_tokens) * 100
        }

def test_token_manager():
    """测试Token管理器"""
    print("🧪 测试Token管理器")
    print("=" * 50)
    
    tm = TokenManager(max_tokens=32768)
    
    # 测试文本
    test_text = "这是一个测试文本。" * 1000
    
    print(f"原始文本长度: {len(test_text)} 字符")
    print(f"原始文本tokens: {tm.count_tokens(test_text)}")
    
    # 截断测试
    truncated = tm.truncate_text(test_text, max_tokens=1000)
    print(f"截断后长度: {len(truncated)} 字符")
    print(f"截断后tokens: {tm.count_tokens(truncated)}")
    
    print("\n✅ Token管理器测试完成")

if __name__ == "__main__":
    test_token_manager()
