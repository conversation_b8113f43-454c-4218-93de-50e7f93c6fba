#!/usr/bin/env python3
"""
SiliconFlow API 简单配置工具
"""

import os
import requests

def test_siliconflow_api(api_key):
    """测试SiliconFlow API"""
    print(f"🔧 测试SiliconFlow API...")
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试聊天API
        data = {
            "model": "Qwen/Qwen2.5-7B-Instruct",
            "messages": [
                {"role": "user", "content": "你好，请简单回复'连接成功'"}
            ],
            "max_tokens": 10
        }
        
        response = requests.post(
            "https://api.siliconflow.cn/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ SiliconFlow API测试成功!")
            print(f"   模型响应: {content}")
            return True
        else:
            print(f"❌ API测试失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def configure_main_py(api_key):
    """配置main.py文件"""
    print(f"📝 配置main.py文件...")
    
    try:
        # 读取main.py
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换API密钥
        content = content.replace(
            'os.environ["OPENAI_API_KEY"] = "YOUR_API_KEY_HERE"',
            f'os.environ["OPENAI_API_KEY"] = "{api_key}"'
        )
        
        # 添加SiliconFlow配置
        content = content.replace(
            'os.environ["OPENAI_API_KEY"] = "YOUR_API_KEY_HERE"',
            f'os.environ["OPENAI_API_KEY"] = "{api_key}"\nos.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"'
        )
        
        # 写回文件
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ main.py配置完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置main.py失败: {e}")
        return False

def main():
    print("🌟 SiliconFlow API 配置工具")
    print("=" * 50)
    
    print("📋 配置步骤:")
    print("1. 访问 https://cloud.siliconflow.cn")
    print("2. 注册并登录账户")
    print("3. 在控制台获取API密钥")
    print("4. 将API密钥输入到下面")
    
    print("\n💡 SiliconFlow优势:")
    print("   ✅ 国内可直接访问，无需代理")
    print("   ✅ 兼容OpenAI API格式")
    print("   ✅ 多个免费中文模型")
    print("   ✅ 价格便宜")
    
    # 获取API密钥
    api_key = input("\n🔑 请输入您的SiliconFlow API密钥: ").strip()
    
    if not api_key:
        print("❌ 请提供有效的API密钥")
        return
    
    # 测试API
    if not test_siliconflow_api(api_key):
        print("❌ API测试失败，请检查密钥是否正确")
        return
    
    # 配置main.py
    if configure_main_py(api_key):
        print("\n🎉 配置完成!")
        print("=" * 50)
        print("📝 现在可以运行:")
        print("   python main.py")
        print("\n🤖 将使用以下配置:")
        print(f"   API提供商: SiliconFlow")
        print(f"   API地址: https://api.siliconflow.cn/v1")
        print(f"   模型: Qwen/Qwen2.5-7B-Instruct (免费)")
        print(f"   数据源: Tushare (A股)")
    else:
        print("❌ 配置失败")

if __name__ == "__main__":
    main()
