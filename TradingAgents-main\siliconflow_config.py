#!/usr/bin/env python3
"""
SiliconFlow API配置和测试工具
"""

import os
import requests
from openai import OpenAI

def test_siliconflow_connection():
    """测试SiliconFlow API连接"""
    print("🔧 测试SiliconFlow API连接...")
    
    # 您需要在这里填入您的SiliconFlow API密钥
    api_key = input("请输入您的SiliconFlow API密钥: ").strip()
    
    if not api_key:
        print("❌ 请提供有效的API密钥")
        return False, None
    
    try:
        # 测试API连接
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 获取模型列表
        response = requests.get(
            "https://api.siliconflow.cn/v1/models",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ SiliconFlow API连接成功！")
            print(f"   可用模型数量: {len(models.get('data', []))}")
            
            # 显示一些免费模型
            free_models = [
                "Qwen/Qwen2.5-7B-Instruct",
                "THUDM/glm-4-9b-chat", 
                "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
                "Qwen/Qwen2.5-Coder-7B-Instruct"
            ]
            
            print("   推荐的免费模型:")
            for model in free_models:
                print(f"     - {model}")
            
            return True, api_key
            
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False, None

def test_siliconflow_chat(api_key, model="Qwen/Qwen2.5-7B-Instruct"):
    """测试SiliconFlow聊天功能"""
    print(f"\n🤖 测试SiliconFlow聊天功能 (模型: {model})...")
    
    try:
        # 创建OpenAI客户端，但使用SiliconFlow的API
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        
        # 测试聊天
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己，并说明你可以帮助分析A股股票。"}
            ],
            max_tokens=200
        )
        
        result = response.choices[0].message.content
        print(f"✅ 聊天测试成功！")
        print(f"   模型响应: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        return False

def test_a_stock_analysis_with_siliconflow(api_key):
    """使用SiliconFlow进行A股分析测试"""
    print(f"\n📊 测试A股分析功能...")
    
    try:
        # 获取A股数据
        from tradingagents.dataflows.tushare_utils import (
            initialize_tushare, 
            get_a_stock_daily_data, 
            get_a_stock_basic_info
        )
        
        # 初始化Tushare
        tushare_token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        if not initialize_tushare(tushare_token):
            print("❌ Tushare初始化失败")
            return False
        
        # 获取股票数据
        symbol = "000001"
        basic_info = get_a_stock_basic_info(symbol)
        price_data = get_a_stock_daily_data(symbol, "2024-11-25", "2024-11-29")
        
        # 使用SiliconFlow分析
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        
        prompt = f"""
请分析以下A股股票数据：

{basic_info}

{price_data}

请从以下角度进行分析：
1. 基本面分析
2. 技术面分析  
3. 投资建议
4. 风险提示

请用中文回答，控制在300字以内。
"""
        
        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-7B-Instruct",
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=500
        )
        
        analysis = response.choices[0].message.content
        print(f"✅ A股分析测试成功！")
        print(f"\n📋 AI分析结果:")
        print("-" * 50)
        print(analysis)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ A股分析测试失败: {e}")
        return False

def create_siliconflow_config(api_key):
    """创建SiliconFlow配置文件"""
    print(f"\n📝 创建SiliconFlow配置...")
    
    # 修改main.py配置
    main_py_content = f'''import os
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
from tradingagents.dataflows.tushare_utils import initialize_tushare

# 设置SiliconFlow API配置
os.environ["OPENAI_API_KEY"] = "{api_key}"
os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"
os.environ["FINNHUB_API_KEY"] = "demo"  # 使用demo密钥，因为我们主要用tushare

# 初始化Tushare
tushare_token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
initialize_tushare(tushare_token)

# Create a custom config for A股分析
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "openai"  # 使用OpenAI兼容接口
config["backend_url"] = "https://api.siliconflow.cn/v1"  # SiliconFlow API地址
config["deep_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"  # 免费的中文模型
config["quick_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"  # 免费的中文模型
config["max_debate_rounds"] = 1
config["online_tools"] = True  # 使用在线工具获取A股数据
config["market_type"] = "a_stock"  # 设置为A股市场

print("🚀 TradingAgents A股分析系统启动 (SiliconFlow)")
print("=" * 50)
print(f"📊 分析市场: 中国A股")
print(f"🤖 LLM提供商: SiliconFlow")
print(f"🧠 AI模型: {{config['deep_think_llm']}}")
print(f"📈 Tushare已初始化")
print("=" * 50)

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# 分析A股股票 - 以平安银行为例
stock_symbol = "000001"  # 平安银行
analysis_date = "2024-12-01"

print(f"\\n🎯 开始分析A股股票: {{stock_symbol}}")
print(f"📅 分析日期: {{analysis_date}}")
print("\\n⏳ 正在运行多智能体分析...")

try:
    # forward propagate
    _, decision = ta.propagate(stock_symbol, analysis_date)
    
    print("\\n" + "=" * 50)
    print("📋 分析结果:")
    print("=" * 50)
    print(decision)
    
except Exception as e:
    print(f"\\n❌ 分析过程中出现错误: {{e}}")
    print("请检查API密钥和网络连接")

print("\\n🎉 分析完成!")
'''
    
    # 保存配置文件
    with open("main_siliconflow.py", "w", encoding="utf-8") as f:
        f.write(main_py_content)
    
    print("✅ SiliconFlow配置文件已创建: main_siliconflow.py")
    
    # 创建环境变量设置脚本
    env_script = f'''#!/usr/bin/env python3
"""
SiliconFlow环境变量设置
"""

import os

def setup_siliconflow_env():
    """设置SiliconFlow环境变量"""
    os.environ["OPENAI_API_KEY"] = "{api_key}"
    os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"
    print("✅ SiliconFlow环境变量已设置")

if __name__ == "__main__":
    setup_siliconflow_env()
'''
    
    with open("setup_siliconflow.py", "w", encoding="utf-8") as f:
        f.write(env_script)
    
    print("✅ 环境设置脚本已创建: setup_siliconflow.py")

def main():
    print("🌟 SiliconFlow API配置工具")
    print("=" * 50)
    
    print("📋 SiliconFlow优势:")
    print("   ✅ 国内可直接访问，无需代理")
    print("   ✅ 兼容OpenAI API格式")
    print("   ✅ 多个免费中文模型")
    print("   ✅ 价格比OpenAI便宜很多")
    print("   ✅ 支持Qwen、GLM、DeepSeek等模型")
    
    print("\n🔑 获取API密钥:")
    print("   1. 访问 https://cloud.siliconflow.cn")
    print("   2. 注册并登录账户")
    print("   3. 在控制台获取API密钥")
    
    # 测试连接
    success, api_key = test_siliconflow_connection()
    
    if not success:
        print("\n❌ 连接失败，请检查API密钥")
        return
    
    # 测试聊天
    if test_siliconflow_chat(api_key):
        print("\n✅ 基础聊天功能正常")
    
    # 测试A股分析
    if test_a_stock_analysis_with_siliconflow(api_key):
        print("\n✅ A股分析功能正常")
    
    # 创建配置文件
    create_siliconflow_config(api_key)
    
    print("\n🎉 SiliconFlow配置完成！")
    print("=" * 50)
    print("📝 使用方法:")
    print("   1. 运行完整A股分析:")
    print("      python main_siliconflow.py")
    print("   2. 或者设置环境变量后运行原程序:")
    print("      python setup_siliconflow.py")
    print("      python main.py")
    
    print("\n💡 推荐模型:")
    print("   - Qwen/Qwen2.5-7B-Instruct (免费，中文优秀)")
    print("   - THUDM/glm-4-9b-chat (免费，中文对话)")
    print("   - deepseek-ai/DeepSeek-R1-Distill-Qwen-7B (免费，推理能力强)")

if __name__ == "__main__":
    main()
