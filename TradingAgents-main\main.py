import os
import json
import ast
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
from tradingagents.dataflows.tushare_utils import initialize_tushare
from langchain_core.messages import AIMessage
from token_manager import TokenManager

# 修复SiliconFlow工具调用格式问题
def fix_tool_call_args(tool_call_args):
    """修复工具调用参数格式"""
    if isinstance(tool_call_args, dict):
        return tool_call_args

    if isinstance(tool_call_args, str):
        try:
            return json.loads(tool_call_args)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(tool_call_args)
            except (ValueError, SyntaxError):
                print(f"警告: 无法解析工具调用参数: {tool_call_args}")
                return {}
    return tool_call_args

# 修补AIMessage的工具调用验证
original_init = AIMessage.__init__
def patched_init(self, content="", **kwargs):
    if "tool_calls" in kwargs and kwargs["tool_calls"]:
        fixed_tool_calls = []
        for tool_call in kwargs["tool_calls"]:
            if hasattr(tool_call, 'args'):
                tool_call.args = fix_tool_call_args(tool_call.args)
            elif isinstance(tool_call, dict) and 'args' in tool_call:
                tool_call['args'] = fix_tool_call_args(tool_call['args'])
            fixed_tool_calls.append(tool_call)
        kwargs["tool_calls"] = fixed_tool_calls
    original_init(self, content, **kwargs)

AIMessage.__init__ = patched_init

# 选择API提供商 (请选择其中一种)

# 方案1: 使用SiliconFlow (推荐 - 国内可直接访问)
# 请在 https://cloud.siliconflow.cn 获取API密钥，然后取消注释以下行:
# os.environ["OPENAI_API_KEY"] = "YOUR_SILICONFLOW_API_KEY"
# os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"

# 方案2: 使用OpenAI (需要代理)
# 请根据您的代理软件配置取消注释并修改以下行:
# os.environ["HTTP_PROXY"] = "http://127.0.0.1:7890"   # Clash代理
# os.environ["HTTPS_PROXY"] = "http://127.0.0.1:7890"  # Clash代理
# os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"

# SiliconFlow配置 (已配置您的API密钥)
os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"
os.environ["FINNHUB_API_KEY"] = "demo"  # 使用demo密钥，因为我们主要用tushare

# 初始化Tushare
tushare_token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
initialize_tushare(tushare_token)

# Create a custom config for A股分析
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "openai"  # 使用OpenAI兼容接口

# 根据API提供商设置后端URL和模型
if "OPENAI_BASE_URL" in os.environ:
    # 使用SiliconFlow
    config["backend_url"] = os.environ["OPENAI_BASE_URL"]
    config["deep_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"  # 免费中文模型
    config["quick_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"  # 免费中文模型
    print("🌟 使用SiliconFlow API")
else:
    # 使用OpenAI
    config["backend_url"] = "https://api.openai.com/v1"
    config["deep_think_llm"] = "gpt-4o-mini"
    config["quick_think_llm"] = "gpt-4o-mini"
    print("🤖 使用OpenAI API")

config["max_debate_rounds"] = 1
config["online_tools"] = True  # 使用在线工具获取A股数据
config["market_type"] = "a_stock"  # 设置为A股市场

# 针对SiliconFlow的token限制优化配置
config["max_seq_len"] = 32768  # SiliconFlow Qwen模型的最大token限制
config["data_limit_days"] = 30  # 限制数据获取天数
config["max_data_rows"] = 15   # 限制数据行数

# 初始化Token管理器
token_manager = TokenManager(max_tokens=32768)
print(f"🔧 Token管理器已初始化 (限制: {token_manager.effective_limit} tokens)")

print("🚀 TradingAgents A股分析系统启动")
print("=" * 50)
print(f"📊 分析市场: 中国A股")
print(f"🤖 LLM模型: {config['deep_think_llm']}")
print(f"📈 Tushare已初始化")
print("=" * 50)

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# 分析A股股票 - 以平安银行为例
stock_symbol = "601011"  # 输入要分析的股票代码 (兴业银行)
analysis_date = "2024-12-01"  # 使用有效的历史交易日

print(f"\n🎯 开始分析A股股票: {stock_symbol}")
print(f"📅 分析日期: {analysis_date}")
print("\n⏳ 正在运行多智能体分析...")

try:
    # forward propagate
    _, decision = ta.propagate(stock_symbol, analysis_date)

    print("\n" + "=" * 50)
    print("📋 分析结果:")
    print("=" * 50)
    print(decision)

except Exception as e:
    print(f"\n❌ 分析过程中出现错误: {e}")
    print("请检查API密钥和网络连接")

print("\n🎉 分析完成!")

# Memorize mistakes and reflect
# ta.reflect_and_remember(1000) # parameter is the position returns
