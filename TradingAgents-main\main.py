import os
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
from tradingagents.dataflows.tushare_utils import initialize_tushare

# 设置API密钥
os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
os.environ["FINNHUB_API_KEY"] = "demo"  # 使用demo密钥，因为我们主要用tushare

# 初始化Tushare
tushare_token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
initialize_tushare(tushare_token)

# Create a custom config for A股分析
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "openai"
config["backend_url"] = "https://api.openai.com/v1"
config["deep_think_llm"] = "gpt-4o-mini"
config["quick_think_llm"] = "gpt-4o-mini"
config["max_debate_rounds"] = 1
config["online_tools"] = True  # 使用在线工具获取A股数据
config["market_type"] = "a_stock"  # 设置为A股市场

print("🚀 TradingAgents A股分析系统启动")
print("=" * 50)
print(f"📊 分析市场: 中国A股")
print(f"🤖 LLM模型: {config['deep_think_llm']}")
print(f"📈 Tushare已初始化")
print("=" * 50)

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# 分析A股股票 - 以平安银行为例
stock_symbol = "000001"  # 平安银行
analysis_date = "2024-12-01"

print(f"\n🎯 开始分析A股股票: {stock_symbol}")
print(f"📅 分析日期: {analysis_date}")
print("\n⏳ 正在运行多智能体分析...")

try:
    # forward propagate
    _, decision = ta.propagate(stock_symbol, analysis_date)

    print("\n" + "=" * 50)
    print("📋 分析结果:")
    print("=" * 50)
    print(decision)

except Exception as e:
    print(f"\n❌ 分析过程中出现错误: {e}")
    print("请检查API密钥和网络连接")

print("\n🎉 分析完成!")

# Memorize mistakes and reflect
# ta.reflect_and_remember(1000) # parameter is the position returns
