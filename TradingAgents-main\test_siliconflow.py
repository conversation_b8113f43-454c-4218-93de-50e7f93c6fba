#!/usr/bin/env python3
"""
测试SiliconFlow API连接
"""

import requests
import json

def test_siliconflow_api():
    """测试SiliconFlow API连接"""
    print("🔧 测试SiliconFlow API连接...")
    
    api_key = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试聊天API
    data = {
        "model": "Qwen/Qwen2.5-7B-Instruct",
        "messages": [
            {"role": "user", "content": "你好，请简单回复'连接成功'"}
        ],
        "max_tokens": 20
    }
    
    try:
        response = requests.post(
            "https://api.siliconflow.cn/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=15
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ SiliconFlow API连接成功!")
            print(f"   模型响应: {content}")
            print(f"   使用模型: Qwen/Qwen2.5-7B-Instruct (免费)")
            return True
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_openai_client():
    """测试OpenAI客户端兼容性"""
    print("\n🤖 测试OpenAI客户端兼容性...")
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            api_key="sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh",
            base_url="https://api.siliconflow.cn/v1"
        )
        
        response = client.chat.completions.create(
            model="Qwen/Qwen2.5-7B-Instruct",
            messages=[
                {"role": "user", "content": "请用中文回复'OpenAI客户端兼容测试成功'"}
            ],
            max_tokens=30
        )
        
        content = response.choices[0].message.content
        print(f"✅ OpenAI客户端兼容测试成功!")
        print(f"   模型响应: {content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI客户端测试失败: {e}")
        return False

def main():
    print("🌟 SiliconFlow API 连接测试")
    print("=" * 50)
    
    # 测试直接API调用
    api_success = test_siliconflow_api()
    
    # 测试OpenAI客户端兼容性
    client_success = test_openai_client()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"   直接API调用: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"   OpenAI客户端: {'✅ 成功' if client_success else '❌ 失败'}")
    
    if api_success and client_success:
        print("\n🎉 SiliconFlow API配置完全成功!")
        print("   现在可以运行完整的A股分析:")
        print("   python main.py")
    else:
        print("\n❌ 配置存在问题，请检查API密钥")

if __name__ == "__main__":
    main()
