#!/usr/bin/env python3
"""
OpenAI API连接诊断工具
"""

import os
import time
import requests
from openai import OpenAI

def test_basic_connectivity():
    """测试基本网络连接"""
    print("🌐 测试基本网络连接...")
    
    try:
        # 测试访问OpenAI官网
        response = requests.get("https://api.openai.com", timeout=10)
        print(f"✅ OpenAI API服务器可达，状态码: {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        print("❌ 网络连接超时，可能需要VPN或代理")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到OpenAI服务器，请检查网络")
        return False
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False

def test_api_key_format():
    """测试API密钥格式"""
    print("\n🔑 测试API密钥格式...")
    
    api_key = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
    
    # 检查密钥格式
    if not api_key.startswith("sk-"):
        print("❌ API密钥格式错误，应该以'sk-'开头")
        return False
    
    if len(api_key) < 20:
        print("❌ API密钥长度太短")
        return False
    
    print(f"✅ API密钥格式正确: {api_key[:10]}...{api_key[-10:]}")
    return True

def test_api_key_validity():
    """测试API密钥有效性"""
    print("\n🔍 测试API密钥有效性...")
    
    api_key = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
    
    try:
        # 使用requests直接测试
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            "https://api.openai.com/v1/models",
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ API密钥有效，可访问 {len(models.get('data', []))} 个模型")
            return True
        elif response.status_code == 401:
            print("❌ API密钥无效或已过期")
            print(f"   错误信息: {response.text}")
            return False
        elif response.status_code == 429:
            print("⚠️  API调用频率限制，请稍后重试")
            return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        return False
    except Exception as e:
        print(f"❌ API密钥测试失败: {e}")
        return False

def test_openai_client():
    """测试OpenAI客户端"""
    print("\n🤖 测试OpenAI客户端...")
    
    try:
        # 设置API密钥
        os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
        
        # 创建客户端
        client = OpenAI(timeout=20.0)  # 增加超时时间
        
        # 测试简单请求
        print("   发送测试请求...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",  # 使用更便宜的模型测试
            messages=[
                {"role": "user", "content": "请回复'连接成功'"}
            ],
            max_tokens=10,
            timeout=20
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI客户端工作正常，响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI客户端测试失败: {e}")
        
        # 尝试使用gpt-4o-mini
        try:
            print("   尝试使用gpt-4o-mini模型...")
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "user", "content": "测试"}
                ],
                max_tokens=5,
                timeout=20
            )
            result = response.choices[0].message.content
            print(f"✅ gpt-4o-mini模型工作正常: {result}")
            return True
        except Exception as e2:
            print(f"❌ gpt-4o-mini也失败: {e2}")
            return False

def test_with_proxy():
    """测试使用代理连接"""
    print("\n🔄 测试代理连接...")
    
    # 常见的代理设置
    proxy_configs = [
        {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"},  # Clash
        {"http": "http://127.0.0.1:1080", "https": "http://127.0.0.1:1080"},  # Shadowsocks
        {"http": "http://127.0.0.1:8080", "https": "http://127.0.0.1:8080"},  # 其他代理
    ]
    
    for i, proxy in enumerate(proxy_configs):
        try:
            print(f"   尝试代理配置 {i+1}: {proxy['http']}")
            
            response = requests.get(
                "https://api.openai.com",
                proxies=proxy,
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"✅ 代理 {proxy['http']} 可用")
                return proxy
                
        except Exception as e:
            print(f"   代理 {proxy['http']} 不可用: {e}")
    
    print("❌ 未找到可用的代理")
    return None

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    print("=" * 50)
    
    print("1. 🌐 网络连接问题:")
    print("   - 检查防火墙设置")
    print("   - 尝试使用VPN或代理")
    print("   - 检查公司/学校网络限制")
    
    print("\n2. 🔑 API密钥问题:")
    print("   - 登录 https://platform.openai.com/api-keys")
    print("   - 检查密钥是否有效")
    print("   - 确认账户余额充足")
    print("   - 检查密钥权限设置")
    
    print("\n3. ⚙️ 配置优化:")
    print("   - 增加请求超时时间")
    print("   - 使用更便宜的模型(gpt-3.5-turbo)")
    print("   - 减少并发请求数量")
    
    print("\n4. 🔄 替代方案:")
    print("   - 使用其他LLM提供商(Claude, Gemini)")
    print("   - 配置本地模型")
    print("   - 暂时使用离线模式")

def main():
    print("=" * 60)
    print("🔧 OpenAI API连接诊断工具")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("基本网络连接", test_basic_connectivity),
        ("API密钥格式", test_api_key_format),
        ("API密钥有效性", test_api_key_validity),
        ("OpenAI客户端", test_openai_client),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
        
        time.sleep(1)  # 避免请求过快
    
    # 如果基本连接失败，尝试代理
    if not results.get("基本网络连接", False):
        proxy = test_with_proxy()
        if proxy:
            print(f"\n✅ 找到可用代理: {proxy}")
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！OpenAI API连接正常！")
        print("   可以运行完整的A股分析: python main.py")
    else:
        suggest_solutions()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
