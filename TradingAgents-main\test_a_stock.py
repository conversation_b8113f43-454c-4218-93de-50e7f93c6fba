#!/usr/bin/env python3
"""
A股系统简单测试脚本
"""

import os
from tradingagents.dataflows.tushare_utils import (
    initialize_tushare, 
    get_a_stock_daily_data, 
    get_a_stock_basic_info,
    get_a_stock_financial_data,
    get_market_index_data
)

def test_openai_connection():
    """测试OpenAI连接"""
    print("🔧 测试OpenAI API连接...")
    
    try:
        from openai import OpenAI
        
        # 设置API密钥
        os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
        
        client = OpenAI()
        
        # 简单测试
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "Hello, this is a test. Please respond with 'API working'."}],
            max_tokens=10,
            timeout=10
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenAI API响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API测试失败: {e}")
        return False

def test_a_stock_data():
    """测试A股数据获取"""
    print("\n🔧 测试A股数据获取...")
    
    # 初始化Tushare
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    success = initialize_tushare(token)
    
    if not success:
        print("❌ Tushare初始化失败")
        return False
    
    try:
        print("\n📊 获取平安银行基本信息...")
        basic_info = get_a_stock_basic_info("000001")
        print(basic_info[:200] + "..." if len(basic_info) > 200 else basic_info)
        
        print("\n📈 获取平安银行交易数据...")
        daily_data = get_a_stock_daily_data("000001", "2024-11-25", "2024-11-29")
        print(daily_data[:300] + "..." if len(daily_data) > 300 else daily_data)
        
        print("\n💰 获取平安银行财务数据...")
        financial_data = get_a_stock_financial_data("000001")
        print(financial_data[:200] + "..." if len(financial_data) > 200 else financial_data)
        
        print("\n📊 获取上证指数数据...")
        index_data = get_market_index_data("000001.SH", "2024-11-25", "2024-11-29")
        print(index_data[:300] + "..." if len(index_data) > 300 else index_data)
        
        print("✅ A股数据获取测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ A股数据获取失败: {e}")
        return False

def test_simple_analysis():
    """测试简单的AI分析"""
    print("\n🔧 测试简单AI分析...")
    
    try:
        from openai import OpenAI
        
        client = OpenAI()
        
        # 获取股票数据
        stock_data = get_a_stock_daily_data("000001", "2024-11-25", "2024-11-29")
        basic_info = get_a_stock_basic_info("000001")
        
        # 简单分析请求
        prompt = f"""
请分析以下A股股票数据：

{basic_info}

{stock_data}

请给出简短的投资建议（不超过100字）。
"""
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            timeout=30
        )
        
        analysis = response.choices[0].message.content
        print(f"🤖 AI分析结果:\n{analysis}")
        
        print("✅ 简单AI分析测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ AI分析测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 TradingAgents A股系统功能测试")
    print("=" * 60)
    
    # 测试各个组件
    tests = [
        ("OpenAI API连接", test_openai_connection),
        ("A股数据获取", test_a_stock_data),
        ("简单AI分析", test_simple_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！A股分析系统配置成功！")
        print("\n🚀 可以运行完整分析:")
        print("   python main.py")
    else:
        print("⚠️  部分测试失败，请检查配置")
        print("\n🔧 故障排除建议:")
        print("   1. 检查网络连接")
        print("   2. 验证API密钥是否有效")
        print("   3. 确认Tushare token权限")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
