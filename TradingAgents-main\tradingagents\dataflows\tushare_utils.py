"""
Tushare数据接口工具
用于获取中国A股市场数据
"""

import tushare as ts
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Annotated, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量存储tushare pro实例
_ts_pro = None

def initialize_tushare(token: str):
    """初始化Tushare Pro接口"""
    global _ts_pro
    try:
        ts.set_token(token)
        _ts_pro = ts.pro_api()
        logger.info("Tushare Pro API初始化成功")
        return True
    except Exception as e:
        logger.error(f"Tushare Pro API初始化失败: {e}")
        return False

def get_tushare_pro():
    """获取tushare pro实例"""
    global _ts_pro
    if _ts_pro is None:
        # 尝试使用默认token初始化
        token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        initialize_tushare(token)
    return _ts_pro

def convert_ts_code(symbol: str) -> str:
    """
    将股票代码转换为tushare格式
    例: 000001 -> 000001.SZ, 600000 -> 600000.SH
    """
    if '.' in symbol:
        return symbol
    
    # 判断市场
    if symbol.startswith('00') or symbol.startswith('30'):
        return f"{symbol}.SZ"  # 深圳
    elif symbol.startswith('60') or symbol.startswith('68'):
        return f"{symbol}.SH"  # 上海
    else:
        # 默认深圳
        return f"{symbol}.SZ"

def get_a_stock_daily_data(
    symbol: Annotated[str, "A股股票代码，如000001或000001.SZ"],
    start_date: Annotated[str, "开始日期，格式YYYY-MM-DD"],
    end_date: Annotated[str, "结束日期，格式YYYY-MM-DD"]
) -> str:
    """
    获取A股日线数据
    
    Args:
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        str: 格式化的股票数据
    """
    try:
        pro = get_tushare_pro()
        if pro is None:
            return "Tushare API未初始化"
        
        # 转换股票代码格式
        ts_code = convert_ts_code(symbol)
        
        # 转换日期格式 (YYYY-MM-DD -> YYYYMMDD)
        start_date_ts = start_date.replace('-', '')
        end_date_ts = end_date.replace('-', '')
        
        # 获取日线数据
        df = pro.daily(ts_code=ts_code, start_date=start_date_ts, end_date=end_date_ts)
        
        if df.empty:
            return f"未找到股票 {symbol} 在 {start_date} 到 {end_date} 期间的数据"
        
        # 按日期排序
        df = df.sort_values('trade_date')
        
        # 格式化输出
        result = f"## {symbol} A股日线数据 ({start_date} 到 {end_date}):\n\n"
        result += "日期\t\t开盘\t最高\t最低\t收盘\t成交量\t成交额\n"
        result += "-" * 60 + "\n"
        
        for _, row in df.iterrows():
            date_str = f"{row['trade_date'][:4]}-{row['trade_date'][4:6]}-{row['trade_date'][6:]}"
            result += f"{date_str}\t{row['open']:.2f}\t{row['high']:.2f}\t{row['low']:.2f}\t{row['close']:.2f}\t{row['vol']:.0f}\t{row['amount']:.2f}\n"
        
        return result
        
    except Exception as e:
        logger.error(f"获取A股日线数据失败: {e}")
        return f"获取数据失败: {str(e)}"

def get_a_stock_basic_info(
    symbol: Annotated[str, "A股股票代码"]
) -> str:
    """
    获取A股基本信息
    
    Args:
        symbol: 股票代码
    
    Returns:
        str: 股票基本信息
    """
    try:
        pro = get_tushare_pro()
        if pro is None:
            return "Tushare API未初始化"
        
        ts_code = convert_ts_code(symbol)
        
        # 获取股票基本信息
        df = pro.stock_basic(ts_code=ts_code)
        
        if df.empty:
            return f"未找到股票 {symbol} 的基本信息"
        
        stock_info = df.iloc[0]
        
        result = f"## {symbol} 股票基本信息:\n\n"
        result += f"股票代码: {stock_info['ts_code']}\n"
        result += f"股票名称: {stock_info['name']}\n"
        result += f"所属行业: {stock_info['industry']}\n"
        result += f"市场类型: {stock_info['market']}\n"
        result += f"上市日期: {stock_info['list_date']}\n"
        result += f"是否沪深港通标的: {stock_info.get('is_hs', 'N/A')}\n"
        
        return result
        
    except Exception as e:
        logger.error(f"获取股票基本信息失败: {e}")
        return f"获取基本信息失败: {str(e)}"

def get_a_stock_financial_data(
    symbol: Annotated[str, "A股股票代码"],
    period: Annotated[str, "报告期，格式YYYYMMDD"] = None
) -> str:
    """
    获取A股财务数据
    
    Args:
        symbol: 股票代码
        period: 报告期，如果为None则获取最新的
    
    Returns:
        str: 财务数据
    """
    try:
        pro = get_tushare_pro()
        if pro is None:
            return "Tushare API未初始化"
        
        ts_code = convert_ts_code(symbol)
        
        # 获取财务数据
        if period:
            df = pro.income(ts_code=ts_code, period=period)
        else:
            df = pro.income(ts_code=ts_code)
        
        if df.empty:
            return f"未找到股票 {symbol} 的财务数据"
        
        # 获取最新的财务数据
        latest = df.iloc[0]
        
        result = f"## {symbol} 财务数据 (报告期: {latest['end_date']}):\n\n"
        result += f"营业收入: {latest.get('revenue', 'N/A')} 万元\n"
        result += f"净利润: {latest.get('n_income', 'N/A')} 万元\n"
        result += f"毛利润: {latest.get('gross_profit', 'N/A')} 万元\n"
        result += f"营业利润: {latest.get('operate_profit', 'N/A')} 万元\n"
        result += f"利润总额: {latest.get('total_profit', 'N/A')} 万元\n"
        result += f"基本每股收益: {latest.get('basic_eps', 'N/A')} 元\n"
        
        return result
        
    except Exception as e:
        logger.error(f"获取财务数据失败: {e}")
        return f"获取财务数据失败: {str(e)}"

def get_a_stock_news(
    symbol: Annotated[str, "A股股票代码"],
    start_date: Annotated[str, "开始日期，格式YYYY-MM-DD"],
    end_date: Annotated[str, "结束日期，格式YYYY-MM-DD"]
) -> str:
    """
    获取A股相关新闻（模拟功能，tushare免费版可能不包含新闻数据）
    
    Args:
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        str: 新闻信息
    """
    try:
        # 这里可以集成其他新闻源或使用tushare的新闻接口
        result = f"## {symbol} 相关新闻 ({start_date} 到 {end_date}):\n\n"
        result += "注意: 新闻数据需要tushare高级权限或集成其他新闻源\n"
        result += "建议使用Google新闻或其他新闻API获取相关新闻\n"
        
        return result
        
    except Exception as e:
        logger.error(f"获取新闻数据失败: {e}")
        return f"获取新闻数据失败: {str(e)}"

def get_market_index_data(
    index_code: Annotated[str, "指数代码，如000001.SH(上证指数)"],
    start_date: Annotated[str, "开始日期，格式YYYY-MM-DD"],
    end_date: Annotated[str, "结束日期，格式YYYY-MM-DD"]
) -> str:
    """
    获取市场指数数据
    
    Args:
        index_code: 指数代码
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        str: 指数数据
    """
    try:
        pro = get_tushare_pro()
        if pro is None:
            return "Tushare API未初始化"
        
        # 转换日期格式
        start_date_ts = start_date.replace('-', '')
        end_date_ts = end_date.replace('-', '')
        
        # 获取指数数据
        df = pro.index_daily(ts_code=index_code, start_date=start_date_ts, end_date=end_date_ts)
        
        if df.empty:
            return f"未找到指数 {index_code} 的数据"
        
        # 按日期排序
        df = df.sort_values('trade_date')
        
        result = f"## {index_code} 指数数据 ({start_date} 到 {end_date}):\n\n"
        result += "日期\t\t开盘\t最高\t最低\t收盘\t成交量\t成交额\n"
        result += "-" * 60 + "\n"
        
        for _, row in df.iterrows():
            date_str = f"{row['trade_date'][:4]}-{row['trade_date'][4:6]}-{row['trade_date'][6:]}"
            result += f"{date_str}\t{row['open']:.2f}\t{row['high']:.2f}\t{row['low']:.2f}\t{row['close']:.2f}\t{row['vol']:.0f}\t{row['amount']:.2f}\n"
        
        return result
        
    except Exception as e:
        logger.error(f"获取指数数据失败: {e}")
        return f"获取指数数据失败: {str(e)}"
