#!/usr/bin/env python3
"""
离线A股分析模式
在无法连接OpenAI API时使用本地分析
"""

import os
from tradingagents.dataflows.tushare_utils import (
    initialize_tushare, 
    get_a_stock_daily_data, 
    get_a_stock_basic_info,
    get_a_stock_financial_data,
    get_market_index_data
)

def analyze_stock_fundamentals(symbol):
    """基本面分析"""
    print(f"📊 {symbol} 基本面分析:")
    print("-" * 40)
    
    # 获取基本信息
    basic_info = get_a_stock_basic_info(symbol)
    print(basic_info)
    
    # 获取财务数据
    financial_data = get_a_stock_financial_data(symbol)
    print(financial_data)
    
    return basic_info, financial_data

def analyze_technical_indicators(symbol, start_date, end_date):
    """技术分析"""
    print(f"\n📈 {symbol} 技术分析:")
    print("-" * 40)
    
    # 获取价格数据
    price_data = get_a_stock_daily_data(symbol, start_date, end_date)
    print(price_data)
    
    # 简单技术分析
    lines = price_data.split('\n')
    prices = []
    
    for line in lines:
        if '2024-' in line and '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 5:
                try:
                    close_price = float(parts[4])
                    prices.append(close_price)
                except:
                    continue
    
    if len(prices) >= 2:
        latest_price = prices[-1]
        previous_price = prices[-2]
        change = latest_price - previous_price
        change_pct = (change / previous_price) * 100
        
        print(f"\n💹 技术指标分析:")
        print(f"   最新价格: {latest_price:.2f} 元")
        print(f"   涨跌幅: {change:+.2f} 元 ({change_pct:+.2f}%)")
        
        if len(prices) >= 5:
            avg_5 = sum(prices[-5:]) / 5
            print(f"   5日均价: {avg_5:.2f} 元")
            
            if latest_price > avg_5:
                print("   📈 价格在5日均线之上，短期趋势偏强")
            else:
                print("   📉 价格在5日均线之下，短期趋势偏弱")
    
    return prices

def analyze_market_environment(analysis_date):
    """市场环境分析"""
    print(f"\n🌐 市场环境分析 ({analysis_date}):")
    print("-" * 40)
    
    # 获取主要指数
    from datetime import datetime, timedelta
    
    end_date = analysis_date
    start_date_obj = datetime.strptime(analysis_date, "%Y-%m-%d") - timedelta(days=30)
    start_date = start_date_obj.strftime("%Y-%m-%d")
    
    try:
        sh_index = get_market_index_data("000001.SH", start_date, end_date)
        print("📊 上证指数:")
        print(sh_index[:300] + "..." if len(sh_index) > 300 else sh_index)
    except Exception as e:
        print(f"❌ 获取上证指数失败: {e}")

def generate_investment_advice(symbol, basic_info, financial_data, prices):
    """生成投资建议"""
    print(f"\n💡 {symbol} 投资建议:")
    print("=" * 50)
    
    advice = []
    
    # 基于基本面的建议
    if "银行" in basic_info:
        advice.append("🏦 银行股特点: 关注净息差、不良贷款率、资本充足率")
    
    # 基于价格趋势的建议
    if len(prices) >= 5:
        recent_trend = prices[-1] - prices[-5]
        if recent_trend > 0:
            advice.append("📈 短期趋势: 近期价格呈上升趋势")
        else:
            advice.append("📉 短期趋势: 近期价格呈下降趋势")
    
    # 基于财务数据的建议
    if "净利润" in financial_data and "万元" in financial_data:
        advice.append("💰 盈利能力: 建议关注净利润增长率和ROE指标")
    
    # 风险提示
    advice.extend([
        "⚠️  风险提示: 股市有风险，投资需谨慎",
        "📚 建议: 结合更多信息和专业分析再做决策",
        "🔍 关注: 宏观经济环境、行业政策、公司公告"
    ])
    
    for i, item in enumerate(advice, 1):
        print(f"{i}. {item}")
    
    return advice

def offline_stock_analysis(symbol, analysis_date):
    """完整的离线股票分析"""
    print("🇨🇳 TradingAgents A股离线分析系统")
    print("=" * 60)
    print(f"📊 分析标的: {symbol}")
    print(f"📅 分析日期: {analysis_date}")
    print(f"🔧 模式: 离线分析 (无需OpenAI API)")
    print("=" * 60)
    
    # 初始化Tushare
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    if not initialize_tushare(token):
        print("❌ Tushare初始化失败")
        return
    
    try:
        # 1. 基本面分析
        basic_info, financial_data = analyze_stock_fundamentals(symbol)
        
        # 2. 技术分析
        from datetime import datetime, timedelta
        end_date = analysis_date
        start_date_obj = datetime.strptime(analysis_date, "%Y-%m-%d") - timedelta(days=30)
        start_date = start_date_obj.strftime("%Y-%m-%d")
        
        prices = analyze_technical_indicators(symbol, start_date, end_date)
        
        # 3. 市场环境分析
        analyze_market_environment(analysis_date)
        
        # 4. 生成投资建议
        advice = generate_investment_advice(symbol, basic_info, financial_data, prices)
        
        print(f"\n🎉 {symbol} 离线分析完成!")
        print("=" * 60)
        
        return {
            "symbol": symbol,
            "basic_info": basic_info,
            "financial_data": financial_data,
            "prices": prices,
            "advice": advice
        }
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        return None

def main():
    print("🔧 选择分析模式:")
    print("1. 离线分析平安银行(000001)")
    print("2. 离线分析招商银行(600036)")
    print("3. 离线分析贵州茅台(600519)")
    print("4. 自定义股票分析")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        symbol = "000001"
        name = "平安银行"
    elif choice == "2":
        symbol = "600036"
        name = "招商银行"
    elif choice == "3":
        symbol = "600519"
        name = "贵州茅台"
    elif choice == "4":
        symbol = input("请输入股票代码: ").strip()
        name = symbol
    else:
        print("❌ 无效选择")
        return
    
    analysis_date = input(f"请输入分析日期 (YYYY-MM-DD, 默认2024-12-01): ").strip()
    if not analysis_date:
        analysis_date = "2024-12-01"
    
    print(f"\n🎯 开始离线分析 {name}({symbol})...")
    
    result = offline_stock_analysis(symbol, analysis_date)
    
    if result:
        print(f"\n📄 分析报告已生成，建议保存结果用于投资参考")
    else:
        print(f"\n❌ 分析失败，请检查股票代码和网络连接")

if __name__ == "__main__":
    main()
