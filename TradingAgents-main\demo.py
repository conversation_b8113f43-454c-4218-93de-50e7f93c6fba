#!/usr/bin/env python3
"""
TradingAgents 演示脚本
这个脚本展示了如何使用TradingAgents框架进行股票分析
"""

import os
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def main():
    print("=" * 60)
    print("🚀 TradingAgents 多智能体量化交易系统演示")
    print("=" * 60)
    
    # 检查环境变量
    print("\n📋 检查环境配置...")
    
    openai_key = os.getenv("OPENAI_API_KEY")
    finnhub_key = os.getenv("FINNHUB_API_KEY")
    
    if not openai_key:
        print("⚠️  警告: 未设置 OPENAI_API_KEY 环境变量")
        print("   请设置您的OpenAI API密钥以使用LLM功能")
    else:
        print("✅ OpenAI API密钥已设置")
    
    if not finnhub_key:
        print("⚠️  警告: 未设置 FINNHUB_API_KEY 环境变量")
        print("   请设置您的FinnHub API密钥以获取金融数据")
    else:
        print("✅ FinnHub API密钥已设置")
    
    # 创建配置
    print("\n⚙️  配置系统...")
    config = DEFAULT_CONFIG.copy()
    
    # 如果没有API密钥，使用离线模式
    if not openai_key or not finnhub_key:
        config["online_tools"] = False
        print("📱 使用离线模式（缓存数据）")
    else:
        config["online_tools"] = True
        print("🌐 使用在线模式（实时数据）")
    
    config["llm_provider"] = "openai"
    config["deep_think_llm"] = "gpt-4o-mini"
    config["quick_think_llm"] = "gpt-4o-mini"
    config["max_debate_rounds"] = 1
    
    print(f"   - LLM提供商: {config['llm_provider']}")
    print(f"   - 深度思考模型: {config['deep_think_llm']}")
    print(f"   - 快速思考模型: {config['quick_think_llm']}")
    print(f"   - 最大辩论轮数: {config['max_debate_rounds']}")
    print(f"   - 在线工具: {config['online_tools']}")
    
    # 初始化系统
    print("\n🔧 初始化TradingAgents系统...")
    try:
        ta = TradingAgentsGraph(debug=True, config=config)
        print("✅ 系统初始化成功！")
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return
    
    # 展示系统架构
    print("\n🏗️  系统架构:")
    print("   📊 分析师团队:")
    print("      - 基本面分析师: 评估公司财务和业绩指标")
    print("      - 情绪分析师: 分析社交媒体和公众情绪")
    print("      - 新闻分析师: 监控全球新闻和宏观经济指标")
    print("      - 技术分析师: 使用技术指标检测交易模式")
    
    print("   🔬 研究团队:")
    print("      - 看涨研究员: 寻找积极因素")
    print("      - 看跌研究员: 识别风险因素")
    
    print("   💼 交易员:")
    print("      - 综合分析师和研究员的报告做出交易决策")
    
    print("   ⚖️  风险管理团队:")
    print("      - 评估投资组合风险")
    print("      - 调整交易策略")
    
    print("   📈 投资组合经理:")
    print("      - 批准/拒绝交易提案")
    print("      - 执行最终交易决策")
    
    # 如果有API密钥，可以尝试运行分析
    if openai_key and finnhub_key:
        print("\n🎯 准备运行股票分析...")
        print("   股票代码: NVDA")
        print("   分析日期: 2024-05-10")
        print("\n⚠️  注意: 实际运行需要有效的API密钥和网络连接")
        print("   如需运行完整分析，请确保:")
        print("   1. 设置有效的OpenAI API密钥")
        print("   2. 设置有效的FinnHub API密钥")
        print("   3. 网络连接正常")
        print("\n   运行命令: python main.py")
    else:
        print("\n💡 要运行完整的股票分析，请:")
        print("   1. 获取OpenAI API密钥: https://platform.openai.com/api-keys")
        print("   2. 获取FinnHub API密钥: https://finnhub.io/register")
        print("   3. 设置环境变量:")
        print("      $env:OPENAI_API_KEY='your-openai-key'")
        print("      $env:FINNHUB_API_KEY='your-finnhub-key'")
        print("   4. 运行: python main.py")
    
    print("\n🎉 演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
