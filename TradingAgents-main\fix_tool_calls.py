#!/usr/bin/env python3
"""
修复SiliconFlow工具调用格式问题
"""

import json
import ast
from langchain_core.messages import AIMessage
from typing import Any, Dict

def fix_tool_call_args(tool_call_args):
    """
    修复工具调用参数格式
    将字符串格式的参数转换为字典格式
    """
    if isinstance(tool_call_args, dict):
        return tool_call_args
    
    if isinstance(tool_call_args, str):
        try:
            # 尝试解析JSON
            return json.loads(tool_call_args)
        except json.JSONDecodeError:
            try:
                # 尝试解析Python字典字符串
                return ast.literal_eval(tool_call_args)
            except (ValueError, SyntaxError):
                # 如果都失败了，返回空字典
                print(f"警告: 无法解析工具调用参数: {tool_call_args}")
                return {}
    
    return tool_call_args

def patch_ai_message_validation():
    """
    修补AIMessage的工具调用验证
    """
    original_init = AIMessage.__init__
    
    def patched_init(self, content="", **kwargs):
        # 修复tool_calls参数
        if "tool_calls" in kwargs and kwargs["tool_calls"]:
            fixed_tool_calls = []
            for tool_call in kwargs["tool_calls"]:
                if hasattr(tool_call, 'args'):
                    # 修复args字段
                    tool_call.args = fix_tool_call_args(tool_call.args)
                elif isinstance(tool_call, dict) and 'args' in tool_call:
                    tool_call['args'] = fix_tool_call_args(tool_call['args'])
                fixed_tool_calls.append(tool_call)
            kwargs["tool_calls"] = fixed_tool_calls
        
        # 调用原始初始化方法
        original_init(self, content, **kwargs)
    
    # 替换初始化方法
    AIMessage.__init__ = patched_init
    print("✅ AIMessage工具调用验证已修补")

def test_fix():
    """测试修复功能"""
    print("🧪 测试工具调用参数修复...")
    
    # 测试JSON字符串
    json_str = '{"symbol": "000001", "start_date": "2024-01-01", "end_date": "2024-12-01"}'
    result = fix_tool_call_args(json_str)
    print(f"JSON字符串修复: {result}")
    
    # 测试Python字典字符串
    dict_str = "{'symbol': '000001', 'start_date': '2024-01-01'}"
    result = fix_tool_call_args(dict_str)
    print(f"字典字符串修复: {result}")
    
    # 测试已经是字典的情况
    dict_obj = {"symbol": "000001", "start_date": "2024-01-01"}
    result = fix_tool_call_args(dict_obj)
    print(f"字典对象修复: {result}")

if __name__ == "__main__":
    print("🔧 SiliconFlow工具调用格式修复工具")
    print("=" * 50)
    
    # 应用修补
    patch_ai_message_validation()
    
    # 测试修复功能
    test_fix()
    
    print("\n✅ 修复完成！现在可以正常运行A股分析了。")
    print("   运行命令: python main.py")
