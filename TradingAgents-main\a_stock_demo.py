#!/usr/bin/env python3
"""
TradingAgents A股分析演示脚本
专门用于中国A股市场分析
"""

import os
from tradingagents.dataflows.tushare_utils import initialize_tushare, get_a_stock_daily_data, get_a_stock_basic_info

def test_tushare_connection():
    """测试Tushare连接"""
    print("🔧 测试Tushare连接...")
    
    # 初始化Tushare
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    success = initialize_tushare(token)
    
    if success:
        print("✅ Tushare连接成功!")
        
        # 测试获取数据
        print("\n📊 测试获取平安银行(000001)基本信息...")
        try:
            basic_info = get_a_stock_basic_info("000001")
            print(basic_info)
            
            print("\n📈 测试获取最近5天的交易数据...")
            daily_data = get_a_stock_daily_data("000001", "2024-11-25", "2024-11-29")
            print(daily_data)
            
            return True
        except Exception as e:
            print(f"❌ 数据获取测试失败: {e}")
            return False
    else:
        print("❌ Tushare连接失败!")
        return False

def main():
    print("=" * 60)
    print("🇨🇳 TradingAgents A股多智能体分析系统演示")
    print("=" * 60)
    
    print("\n📋 系统配置:")
    print("   🏢 目标市场: 中国A股市场")
    print("   📊 数据源: Tushare Pro")
    print("   🤖 AI模型: OpenAI GPT-4o-mini")
    print("   🔑 Tushare Token: 已配置")
    print("   🔑 OpenAI API Key: 已配置")
    
    # 测试Tushare连接
    print("\n" + "=" * 40)
    tushare_ok = test_tushare_connection()
    print("=" * 40)
    
    if not tushare_ok:
        print("\n⚠️  Tushare连接失败，请检查token是否有效")
        return
    
    print("\n🏗️  A股分析系统架构:")
    print("   📊 分析师团队:")
    print("      - 基本面分析师: 分析A股公司财务数据、行业地位")
    print("      - 技术分析师: 分析A股K线图、技术指标")
    print("      - 新闻分析师: 分析A股相关新闻、政策影响")
    print("      - 情绪分析师: 分析A股投资者情绪、市场热点")
    
    print("   🔬 研究团队:")
    print("      - 看涨研究员: 寻找A股上涨机会和利好因素")
    print("      - 看跌研究员: 识别A股风险和利空因素")
    
    print("   💼 交易决策:")
    print("      - 交易员: 综合分析制定A股交易策略")
    print("      - 风险管理: 评估A股投资风险")
    print("      - 投资组合经理: 最终投资决策")
    
    print("\n📈 支持的A股功能:")
    print("   ✅ 获取A股实时/历史价格数据")
    print("   ✅ 获取A股公司基本信息")
    print("   ✅ 获取A股财务数据")
    print("   ✅ 获取A股市场指数(上证、深证、创业板)")
    print("   ✅ 多智能体协作分析")
    print("   ✅ 中文分析报告")
    
    print("\n🎯 示例分析股票:")
    popular_stocks = [
        ("000001", "平安银行", "金融"),
        ("000002", "万科A", "房地产"),
        ("600036", "招商银行", "金融"),
        ("600519", "贵州茅台", "食品饮料"),
        ("000858", "五粮液", "食品饮料"),
        ("002415", "海康威视", "电子"),
        ("300059", "东方财富", "金融"),
        ("600276", "恒瑞医药", "医药生物")
    ]
    
    print("   热门A股标的:")
    for code, name, industry in popular_stocks:
        print(f"      {code} - {name} ({industry})")
    
    print("\n🚀 运行完整分析:")
    print("   1. 确保网络连接正常")
    print("   2. 运行命令: python main.py")
    print("   3. 系统将自动分析平安银行(000001)")
    print("   4. 查看详细的多智能体分析报告")
    
    print("\n⚙️  自定义分析:")
    print("   可以修改 main.py 中的以下参数:")
    print("   - stock_symbol: 更改要分析的股票代码")
    print("   - analysis_date: 更改分析日期")
    print("   - max_debate_rounds: 调整辩论轮数")
    
    print("\n💡 使用建议:")
    print("   1. 首次使用建议分析知名大盘股")
    print("   2. 分析日期选择交易日")
    print("   3. 关注系统生成的风险提示")
    print("   4. 结合自己的投资经验做最终决策")
    
    print("\n⚠️  重要提醒:")
    print("   📢 本系统仅供研究和学习使用")
    print("   📢 不构成投资建议，投资有风险")
    print("   📢 请根据自身情况谨慎决策")
    print("   📢 遵守相关法律法规")
    
    print("\n🎉 A股分析系统演示完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
