# SiliconFlow API 配置使用指南

## 🌟 为什么选择SiliconFlow？

### ✅ 主要优势
1. **国内可直接访问** - 无需代理或VPN
2. **兼容OpenAI API** - 可以直接替换OpenAI API
3. **多个免费模型** - 包括Qwen、GLM、DeepSeek等优秀中文模型
4. **价格便宜** - 比OpenAI便宜很多
5. **响应速度快** - 国内服务器，延迟低
6. **中文支持好** - 专门优化的中文大模型

### 📊 推荐模型
| 模型名称 | 价格 | 特点 | 适用场景 |
|---------|------|------|----------|
| Qwen/Qwen2.5-7B-Instruct | 免费 | 中文优秀，通用能力强 | 日常对话、文本分析 |
| THUDM/glm-4-9b-chat | 免费 | 对话能力强 | 智能对话、问答 |
| deepseek-ai/DeepSeek-R1-Distill-Qwen-7B | 免费 | 推理能力强 | 逻辑分析、数学推理 |
| Qwen/Qwen2.5-Coder-7B-Instruct | 免费 | 代码能力强 | 代码生成、技术分析 |

## 🔑 获取API密钥

### 步骤1: 注册账户
1. 访问 [SiliconFlow官网](https://cloud.siliconflow.cn)
2. 点击"注册"按钮
3. 使用邮箱或手机号完成注册
4. 验证邮箱/手机号

### 步骤2: 获取API密钥
1. 登录SiliconFlow控制台
2. 进入"API密钥"页面
3. 点击"创建新密钥"
4. 复制生成的API密钥

### 步骤3: 配置系统
运行配置工具：
```bash
cd TradingAgents-main
python 配置SiliconFlow.py
```

## 🚀 快速配置

### 方法1: 使用配置工具 (推荐)
```bash
# 运行配置工具
python 配置SiliconFlow.py

# 按提示输入API密钥
# 工具会自动配置main.py文件
```

### 方法2: 手动配置
在 `main.py` 文件中找到以下行：
```python
# 临时配置 (请根据上面的方案修改)
os.environ["OPENAI_API_KEY"] = "YOUR_API_KEY_HERE"  # 请替换为您的API密钥
```

替换为：
```python
# SiliconFlow配置
os.environ["OPENAI_API_KEY"] = "你的SiliconFlow API密钥"
os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"
```

## 🧪 测试配置

### 测试1: API连接测试
```bash
python -c "
import requests
headers = {'Authorization': 'Bearer 你的API密钥'}
response = requests.get('https://api.siliconflow.cn/v1/models', headers=headers)
print('API连接:', '成功' if response.status_code == 200 else '失败')
"
```

### 测试2: 聊天功能测试
```bash
python -c "
from openai import OpenAI
client = OpenAI(api_key='你的API密钥', base_url='https://api.siliconflow.cn/v1')
response = client.chat.completions.create(
    model='Qwen/Qwen2.5-7B-Instruct',
    messages=[{'role': 'user', 'content': '你好'}]
)
print('聊天测试:', response.choices[0].message.content)
"
```

### 测试3: A股分析测试
```bash
python a_stock_demo.py
```

## 🎯 运行A股分析

### 完整的多智能体分析
```bash
# 确保已配置SiliconFlow API密钥
python main.py
```

### 系统将执行以下流程：
1. **初始化** - 加载SiliconFlow API和Tushare数据源
2. **数据获取** - 获取A股股票的基本信息、价格数据、财务数据
3. **多智能体分析**:
   - 基本面分析师：分析公司财务状况
   - 技术分析师：分析价格趋势和技术指标
   - 新闻分析师：分析相关新闻和市场情绪
   - 情绪分析师：评估投资者情绪
4. **智能体辩论** - 看涨和看跌研究员进行辩论
5. **风险评估** - 风险管理团队评估投资风险
6. **最终决策** - 投资组合经理做出最终投资建议

## ⚙️ 高级配置

### 自定义模型选择
在 `main.py` 中修改模型配置：
```python
config["deep_think_llm"] = "Qwen/Qwen2.5-14B-Instruct"  # 更强的模型
config["quick_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"   # 快速响应模型
```

### 调整分析参数
```python
config["max_debate_rounds"] = 2  # 增加辩论轮数
config["max_risk_discuss_rounds"] = 2  # 增加风险讨论轮数
```

### 分析不同股票
修改 `main.py` 中的股票代码：
```python
stock_symbol = "600519"  # 贵州茅台
stock_symbol = "000858"  # 五粮液
stock_symbol = "600036"  # 招商银行
```

## 💰 成本控制

### 免费模型 (推荐用于测试)
- `Qwen/Qwen2.5-7B-Instruct`
- `THUDM/glm-4-9b-chat`
- `deepseek-ai/DeepSeek-R1-Distill-Qwen-7B`

### 付费模型 (更强性能)
- `Qwen/Qwen2.5-32B-Instruct` (1.26元/M tokens)
- `deepseek-ai/DeepSeek-V2.5` (1.33元/M tokens)
- `Qwen/Qwen2.5-72B-Instruct` (4.13元/M tokens)

### 成本估算
一次完整的A股分析大约消耗：
- 输入tokens: 5,000-10,000
- 输出tokens: 3,000-8,000
- 使用免费模型：**完全免费**
- 使用付费模型：约0.01-0.1元

## 🔧 故障排除

### 常见问题

#### 1. API密钥错误
```
错误: 401 Unauthorized
解决: 检查API密钥是否正确，是否有效
```

#### 2. 模型不存在
```
错误: Model not found
解决: 检查模型名称是否正确，参考官方模型列表
```

#### 3. 请求超时
```
错误: Request timeout
解决: 检查网络连接，或增加超时时间
```

#### 4. 余额不足
```
错误: Insufficient balance
解决: 充值账户或使用免费模型
```

### 调试技巧
1. **启用调试模式**: 在代码中设置 `debug=True`
2. **查看详细日志**: 检查API请求和响应
3. **逐步测试**: 先测试简单功能，再运行完整分析

## 📞 技术支持

### 官方资源
- **官网**: https://siliconflow.cn
- **文档**: https://docs.siliconflow.cn
- **控制台**: https://cloud.siliconflow.cn

### 社区支持
- **微信群**: 扫描官网二维码加入
- **GitHub**: 查看开源项目和示例

## 🎉 总结

SiliconFlow为TradingAgents提供了完美的API解决方案：

✅ **解决了网络访问问题** - 无需代理即可使用  
✅ **提供了优秀的中文模型** - 更适合A股分析  
✅ **大幅降低了使用成本** - 免费模型 + 低价付费模型  
✅ **保持了完整功能** - 所有多智能体功能都可正常使用  

**现在您可以享受完整的TradingAgents A股多智能体分析体验！** 🇨🇳📈🤖

---
**最后更新**: 2025年7月10日  
**状态**: 推荐使用SiliconFlow替代OpenAI API
