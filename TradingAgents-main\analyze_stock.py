#!/usr/bin/env python3
"""
优化版A股分析脚本 - 解决token长度限制问题
"""

import os
import json
import ast
from datetime import datetime, timedelta
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
from tradingagents.dataflows.tushare_utils import initialize_tushare
from langchain_core.messages import AIMessage

# 修复SiliconFlow工具调用格式问题
def fix_tool_call_args(tool_call_args):
    """修复工具调用参数格式"""
    if isinstance(tool_call_args, dict):
        return tool_call_args
    
    if isinstance(tool_call_args, str):
        try:
            return json.loads(tool_call_args)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(tool_call_args)
            except (ValueError, SyntaxError):
                print(f"警告: 无法解析工具调用参数: {tool_call_args}")
                return {}
    return tool_call_args

# 修补AIMessage的工具调用验证
original_init = AIMessage.__init__
def patched_init(self, content="", **kwargs):
    if "tool_calls" in kwargs and kwargs["tool_calls"]:
        fixed_tool_calls = []
        for tool_call in kwargs["tool_calls"]:
            if hasattr(tool_call, 'args'):
                tool_call.args = fix_tool_call_args(tool_call.args)
            elif isinstance(tool_call, dict) and 'args' in tool_call:
                tool_call['args'] = fix_tool_call_args(tool_call['args'])
            fixed_tool_calls.append(tool_call)
        kwargs["tool_calls"] = fixed_tool_calls
    original_init(self, content, **kwargs)

AIMessage.__init__ = patched_init

def analyze_stock(stock_symbol, analysis_date=None, save_result=True):
    """
    分析指定股票
    
    Args:
        stock_symbol: 股票代码 (如 "601011")
        analysis_date: 分析日期 (如 "2024-12-01")，默认为最近交易日
        save_result: 是否保存结果到文件
    """
    
    print(f"🎯 开始分析股票: {stock_symbol}")
    
    # 设置API配置
    os.environ["OPENAI_API_KEY"] = "sk-chovcyrmledbnnykokxcoznainocpgedivhlefxvjeihnskh"
    os.environ["OPENAI_BASE_URL"] = "https://api.siliconflow.cn/v1"
    os.environ["FINNHUB_API_KEY"] = "demo"
    
    # 初始化Tushare
    tushare_token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    if not initialize_tushare(tushare_token):
        print("❌ Tushare初始化失败")
        return None
    
    # 设置分析日期
    if analysis_date is None:
        # 使用最近的交易日
        today = datetime.now()
        analysis_date = (today - timedelta(days=1)).strftime("%Y-%m-%d")
    
    # 验证日期不是未来日期
    analysis_date_obj = datetime.strptime(analysis_date, "%Y-%m-%d")
    if analysis_date_obj > datetime.now():
        print(f"⚠️  分析日期 {analysis_date} 是未来日期，调整为今天")
        analysis_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"📅 分析日期: {analysis_date}")
    
    # 配置系统 - 优化token使用
    config = DEFAULT_CONFIG.copy()
    config["deep_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"
    config["quick_think_llm"] = "Qwen/Qwen2.5-7B-Instruct"
    config["max_debate_rounds"] = 1  # 减少辩论轮数
    config["max_risk_discuss_rounds"] = 1  # 减少风险讨论轮数
    config["online_tools"] = True
    
    print("🌟 使用SiliconFlow API")
    print("🚀 TradingAgents A股分析系统启动")
    print("=" * 50)
    print(f"📊 分析市场: 中国A股")
    print(f"🤖 LLM模型: {config['deep_think_llm']}")
    print(f"📈 Tushare已初始化")
    print("=" * 50)
    
    try:
        # 创建分析系统
        ta = TradingAgentsGraph(debug=True, config=config)
        
        print(f"🎯 开始分析A股股票: {stock_symbol}")
        print(f"📅 分析日期: {analysis_date}")
        print()
        print("⏳ 正在运行多智能体分析...")
        
        # 运行分析
        _, decision = ta.propagate(stock_symbol, analysis_date)
        
        print(f"\n🎉 分析完成!")
        print(f"📋 投资建议: {decision}")
        
        # 保存结果
        if save_result:
            result = {
                "stock_symbol": stock_symbol,
                "analysis_date": analysis_date,
                "analysis_time": datetime.now().isoformat(),
                "decision": decision,
                "model": config["deep_think_llm"]
            }
            
            filename = f"analysis_{stock_symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 分析结果已保存到: {filename}")
        
        return decision
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        print("💡 建议:")
        print("   1. 检查股票代码是否正确 (6位数字)")
        print("   2. 检查分析日期是否为有效交易日")
        print("   3. 尝试分析其他股票")
        return None

def main():
    """主函数"""
    print("🇨🇳 TradingAgents A股智能分析系统")
    print("=" * 50)
    
    # 热门股票推荐
    popular_stocks = {
        "601011": "兴业银行",
        "000001": "平安银行", 
        "600036": "招商银行",
        "600519": "贵州茅台",
        "000858": "五粮液",
        "002415": "海康威视",
        "300059": "东方财富"
    }
    
    print("📈 热门A股推荐:")
    for code, name in popular_stocks.items():
        print(f"   {code} - {name}")
    print()
    
    # 分析指定股票
    stock_symbol = "601011"  # 兴业银行
    analysis_date = "2024-12-01"
    
    print(f"🎯 当前分析: {stock_symbol} - {popular_stocks.get(stock_symbol, '未知')}")
    print(f"📅 分析日期: {analysis_date}")
    print()
    
    # 执行分析
    result = analyze_stock(stock_symbol, analysis_date)
    
    if result:
        print(f"\n✅ 分析成功完成!")
        print(f"📊 {stock_symbol} 投资建议: {result}")
    else:
        print(f"\n❌ 分析失败，请检查配置")

if __name__ == "__main__":
    main()
