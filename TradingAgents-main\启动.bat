@echo off
chcp 65001 >nul
echo ============================================================
echo 🚀 TradingAgents 多智能体量化交易系统
echo ============================================================
echo.
echo 请选择运行模式:
echo 1. 演示模式 (无需API密钥)
echo 2. CLI交互模式 (需要API密钥)
echo 3. 直接运行分析 (需要API密钥)
echo 4. 查看使用说明
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 🎯 启动演示模式...
    python demo.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo 🎯 启动CLI交互模式...
    echo 请确保已设置API密钥:
    echo $env:OPENAI_API_KEY="your-key"
    echo $env:FINNHUB_API_KEY="your-key"
    echo.
    python -m cli.main
    pause
) else if "%choice%"=="3" (
    echo.
    echo 🎯 启动直接分析模式...
    echo 请确保已设置API密钥:
    echo $env:OPENAI_API_KEY="your-key"
    echo $env:FINNHUB_API_KEY="your-key"
    echo.
    python main.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo 📖 打开使用说明...
    notepad 使用说明.md
) else if "%choice%"=="5" (
    echo.
    echo 👋 再见!
    exit
) else (
    echo.
    echo ❌ 无效选择，请重新运行
    pause
)
